package com.newnary.rpa.safebox.example;

import org.linguafranca.pwdb.Entry;
import org.linguafranca.pwdb.Group;
import org.linguafranca.pwdb.kdbx.KdbxCreds;
import org.linguafranca.pwdb.kdbx.KdbxHeader;
import org.linguafranca.pwdb.kdbx.KdbxStreamFormat;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.linguafranca.pwdb.kdbx.jackson.JacksonEntry;
import org.linguafranca.pwdb.kdbx.jackson.JacksonGroup;
import org.linguafranca.pwdb.kdbx.jackson.JacksonSerializableDatabase;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * <AUTHOR>
 * @since Created on 2025-09-09
 **/
public class KeepassDemo {

    public static void main(String[] args) {
        try {

//            KeePassManager keePassManager = new KeePassManager();
//            JacksonDatabase load = keePassManager.load("D:\\Workspace\\IdeaProjects\\safebox\\src\\main\\resources\\Database2.kdbx", new KdbxCreds("123123".getBytes()));
//            Optional<JacksonEntry> thisIsATestB = keePassManager.getEntryByTitle(load, "This is a test B");
//            thisIsATestB.ifPresent(entry -> {
//                System.out.println("  - 条目: " + entry.getTitle());
//                System.out.println("    用户名: " + entry.getUsername());
//                System.out.println("    URL: " + entry.getUrl());
//                System.out.println("    备注: " + entry.getNotes());
//                // 出于安全考虑，通常不打印密码
//                System.out.println("    密码: " + entry.getPropertyValue(Entry.STANDARD_PROPERTY_NAME_PASSWORD).getValueAsString());
//            });

            // 使用V4默认参数创建空白数据库
            KdbxStreamFormat streamFormat = new KdbxStreamFormat(new KdbxHeader(4));
            JacksonDatabase database = new JacksonDatabase(JacksonSerializableDatabase.createEmptyDatabase(), streamFormat);

            // 获取默认分组
            JacksonGroup rootGroup = database.getRootGroup();

            // 创建密码A
            JacksonEntry entry = JacksonEntry.createEntry(database);
            entry.setTitle("This is a test A");
            entry.setUsername("admin");
            entry.setPassword("adminA");
            entry.setUrl("http://localhost:8080/");
            entry.setNotes("This is a test A !!!");
            rootGroup.addEntry(entry);

            // 创建密码B
            JacksonEntry entryB = JacksonEntry.createEntry(database);
            entryB.setTitle("This is a test B");
            entryB.setUsername("root");
            entryB.setPassword("rootB");
            entryB.setUrl("http://localhost:8080/");
            entryB.setNotes("This is a test B !!!");
            rootGroup.addEntry(entryB);


            try (FileOutputStream outputStream = new FileOutputStream("D:\\Workspace\\IdeaProjects\\safebox\\src\\main\\resources\\Database2.kdbx")) {
                database.save(new KdbxCreds("123123".getBytes()), outputStream);
            }

            // 加载KeePass数据库文件
            InputStream inputStream = Files.newInputStream(Paths.get("D:\\Workspace\\IdeaProjects\\safebox\\src\\main\\resources\\Database2.kdbx"));
            if (inputStream == null) {
                System.err.println("无法找到password_db.kdbx文件");
                return;
            }

            // 创建凭证（这里使用空密码，实际使用时应该提供正确的密码）
            KdbxCreds creds = new KdbxCreds("123123".getBytes());

            // 打开数据库
            JacksonDatabase database2 = JacksonDatabase.load(creds, inputStream);

            // 获取根组
            JacksonGroup rootGroup2 = database2.getRootGroup();

            // 打印数据库结构和条目
            System.out.println("KeePass数据库结构：");
            printGroup(rootGroup2, 0);

        } catch (Exception e) {
            System.err.println("读取KeePass数据库时出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 递归打印组及其条目
     *
     * @param group 要打印的组
     * @param level 缩进级别
     */
    private static void printGroup(Group<?, ?, ?, ?> group, int level) {
        String indent = getIndent(level);
        System.out.println(indent + "+ 组: " + group.getName());

        // 打印当前组中的所有条目
        List<? extends Entry<?, ?, ?, ?>> entries = group.getEntries();
        for (Entry<?, ?, ?, ?> entry : entries) {
            System.out.println(indent + "  - 条目: " + entry.getTitle());
            System.out.println(indent + "    用户名: " + entry.getUsername());
            System.out.println(indent + "    URL: " + entry.getUrl());
            System.out.println(indent + "    备注: " + entry.getNotes());
            // 出于安全考虑，通常不打印密码
            System.out.println(indent + "    密码: " + entry.getPropertyValue(Entry.STANDARD_PROPERTY_NAME_PASSWORD).getValueAsString());
        }

        // 递归打印子组
        for (Group<?, ?, ?, ?> subGroup : group.getGroups()) {
            printGroup(subGroup, level + 1);
        }
    }

    /**
     * 获取缩进字符串
     *
     * @param level 缩进级别
     * @return 缩进字符串
     */
    private static String getIndent(int level) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < level; i++) {
            sb.append("  ");
        }
        return sb.toString();
    }
}
