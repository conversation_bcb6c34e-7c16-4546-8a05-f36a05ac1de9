package com.newnary.rpa.safebox.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.newnary.rpa.safebox.common.SecurityEntry;
import com.newnary.rpa.safebox.common.SecurityLite;
import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.controller.manager.dto.DatabaseMetadataDto;
import com.newnary.rpa.safebox.controller.manager.response.DatabaseMetadata;
import com.newnary.rpa.safebox.controller.manager.response.DatabaseUploadResponse;
import com.newnary.rpa.safebox.exception.BusinessException;
import com.newnary.rpa.safebox.keepass.KeePassManager;
import com.newnary.rpa.safebox.util.SystemIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.linguafranca.pwdb.kdbx.KdbxCreds;
import org.linguafranca.pwdb.kdbx.KdbxHeader;
import org.linguafranca.pwdb.kdbx.KdbxStreamFormat;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.linguafranca.pwdb.kdbx.jackson.JacksonEntry;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 密码管理服务类
 * 提供密码条目的增删改查和主密码管理功能
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@Service
@Slf4j
public class SecurityManagerService {

    @Resource
    private SafeboxProperties safeboxProperties;

    private byte[] kdbxKey;
    private final KeePassManager keepPassManager = new KeePassManager();
    private final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    /**
     * 默认数据库文件名
     */
    private static final String DEFAULT_DB_FILE = "database.kdbx";

    /**
     * 默认用户ID
     */
    private static final String DEFAULT_USER_ID = "default";

    /**
     * 元数据文件名
     */
    private static final String METADATA_FILE = "metadata.json";

    @PostConstruct
    void init() throws NoSuchAlgorithmException, InvalidKeyException {
        /*
         * 计算机器码配合主配置参数重新计算KdbxKey
         **/
        String kdbxKeyMaster = safeboxProperties.getKeepass().getKdbxKey();

        if (StringUtils.isNotEmpty(kdbxKeyMaster)) {
            String systemId;
            try {
                systemId = SystemIdGenerator.generateSystemId();
            } catch (Exception e) {
                systemId = "NO_SYSTEM_ID";
            }

            byte[] key = Base64.getDecoder().decode(kdbxKeyMaster.getBytes(StandardCharsets.UTF_8));
            SecretKeySpec secretKey = new SecretKeySpec(key, "HmacSHA256");

            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKey);
            kdbxKey = mac.doFinal(systemId.getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 上传KeePass数据库
     *
     * @param file             数据库文件
     * @param originalPassword 原始主密码
     * @param newPassword      新主密码（可选，如果为空则使用原始密码）
     * @return 上传结果
     */
    public DatabaseUploadResponse uploadDatabase(MultipartFile file, String originalPassword, String newPassword) {
        try {
            // 1. 验证文件
            validateUploadFile(file);

            // 2. 验证原始主密码能否打开数据库
            JacksonDatabase originalDatabase = validateOriginalMasterPassword(file, originalPassword);

            // 3. 确定最终使用的密码
            String finalPassword = (newPassword != null && !newPassword.trim().isEmpty()) ? newPassword : originalPassword;

            // 4. 创建用户目录结构
            String userId = DEFAULT_USER_ID;
            createUserDirectory(userId);

            // 5. 使用预设参数和新密码重新保存数据库
            Path databasePath = resaveWithPresetParams(originalDatabase, finalPassword, userId);

            // 6. 生成并保存元数据
            DatabaseMetadataDto metadata = generateMetadata(file, originalDatabase, databasePath);
            saveMetadata(metadata, userId);

            // 7. 构建响应
            DatabaseUploadResponse response = buildUploadResponse(metadata);

            // 8. 如果使用了新密码，在响应中添加提示信息
            if (newPassword != null && !newPassword.trim().isEmpty() && !newPassword.equals(originalPassword)) {
                response.setMessage("数据库上传成功，主密码已更新");
            }

            return response;

        } catch (Exception e) {
            log.error("上传数据库失败", e);
            throw new BusinessException("上传数据库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取数据库元数据
     *
     * @return 数据库元数据
     */
    public DatabaseMetadata getDatabaseMetadata() {
        try {
            String userId = DEFAULT_USER_ID;
            Path metadataPath = getUserMetadataPath(userId);

            if (!Files.exists(metadataPath)) {
                throw new BusinessException("数据库不存在，请先上传数据库文件");
            }

            DatabaseMetadataDto dto = objectMapper.readValue(metadataPath.toFile(), DatabaseMetadataDto.class);
            return convertToMetadata(dto);

        } catch (Exception e) {
            log.error("获取数据库元数据失败", e);
            throw new BusinessException("获取数据库元数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除用户数据库
     *
     * @param masterPassword 主密码
     * @return 删除结果
     */
    public Boolean deleteUserDatabase(String masterPassword) {
        try {
            String userId = DEFAULT_USER_ID;

            // 验证主密码
            validateMasterPasswordForUser(masterPassword, userId);

            // 删除用户目录
            Path userDir = getUserDirectory(userId);
            if (Files.exists(userDir)) {
                deleteDirectory(userDir);
                log.info("成功删除用户数据库: {}", userId);
                return true;
            } else {
                throw new BusinessException("数据库不存在");
            }

        } catch (Exception e) {
            log.error("删除数据库失败", e);
            throw new BusinessException("删除数据库失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有条目
     *
     * @param masterPassword 主密码
     * @return 条目列表
     */
    public List<SecurityLite> getAllEntries(String masterPassword) {
        try {
            String userId = DEFAULT_USER_ID;

            // 加载数据库
            Path databasePath = getDatabasePath(userId);
            if (!Files.exists(databasePath)) {
                throw new BusinessException("数据库不存在，请先上传数据库文件");
            }

            JacksonDatabase database = keepPassManager.load(databasePath.toString(), toKdbxCreds(masterPassword));

            // 获取所有条目并转换为SecurityLite
            List<JacksonEntry> entries = keepPassManager.getAllEntries(database);
            return entries.stream()
                    .map(this::convertToSecurityLite)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取所有条目失败", e);
            throw new BusinessException("获取所有条目失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据标题查找密码条目
     *
     * @param masterPassword 主密码
     * @param keywords       搜索项目
     * @return 结果
     **/
    public List<SecurityEntry> searchEntriesByTitle(String masterPassword, Collection<String> keywords) {
        try {
            String userId = DEFAULT_USER_ID;

            // 加载数据库
            Path databasePath = getDatabasePath(userId);
            if (!Files.exists(databasePath)) {
                throw new BusinessException("数据库不存在，请先上传数据库文件");
            }

            JacksonDatabase database = keepPassManager.load(databasePath.toString(), toKdbxCreds(masterPassword));

            Set<String> titles = new HashSet<>(keywords);
            return titles.stream()
                    .map(title -> keepPassManager.getEntryByTitle(database, title))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(entry -> {
                        SecurityEntry securityEntry = new SecurityEntry();
                        securityEntry.setTitle(entry.getTitle());
                        securityEntry.setUsername(entry.getUsername());
                        securityEntry.setPassword(entry.getPassword());
                        return securityEntry;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("搜索条目失败", e);
            throw new BusinessException("搜索条目失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建主密码
     **/
    private KdbxCreds toKdbxCreds(String masterPassword) {
        if (kdbxKey == null || kdbxKey.length == 0) {
            return new KdbxCreds(masterPassword.getBytes());
        }

        ByteArrayInputStream inputStream = new ByteArrayInputStream(kdbxKey);
        return new KdbxCreds(masterPassword.getBytes(), inputStream);
    }

    /**
     * 验证上传文件
     */
    private void validateUploadFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要上传的文件");
        }

        // 验证文件大小
        long maxSize = safeboxProperties.getKeepass().getMaxFileSize();
        if (file.getSize() > maxSize) {
            throw new BusinessException("文件大小超过限制，最大允许: " + (maxSize / 1024 / 1024) + "MiB");
        }

        // 验证文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".kdbx")) {
            throw new BusinessException("只允许上传.kdbx格式的KeePass数据库文件");
        }
    }

    /**
     * 验证主密码能否打开数据库
     */
    private JacksonDatabase validateOriginalMasterPassword(MultipartFile file, String masterPassword) {
        try {
            return JacksonDatabase.load(new KdbxCreds(masterPassword.getBytes()), file.getInputStream());
        } catch (Exception e) {
            throw new BusinessException("主密码错误或数据库文件损坏", e);
        }
    }

    /**
     * 创建用户目录
     */
    private void createUserDirectory(String userId) {
        try {
            Path userDir = getUserDirectory(userId);
            Files.createDirectories(userDir);

            log.info("创建用户目录: {}", userDir);
        } catch (IOException e) {
            throw new BusinessException("创建用户目录失败", e);
        }
    }

    /**
     * 使用预设参数重新保存数据库
     */
    private Path resaveWithPresetParams(JacksonDatabase originalDatabase, String masterPassword, String userId) {
        try {
            // 创建新数据库使用预设参数
            JacksonDatabase newDatabase = keepPassManager.createEmptyDatabase();

            // 复制所有条目到新数据库
            newDatabase.getRootGroup().copy(originalDatabase.getRootGroup());

            // 保存到用户目录
            Path databasePath = getDatabasePath(userId);
            keepPassManager.save(newDatabase, databasePath.toString(), toKdbxCreds(masterPassword));

            log.info("使用预设参数保存数据库: {}", databasePath);
            return databasePath;
        } catch (Exception e) {
            throw new BusinessException("重新保存数据库失败", e);
        }
    }

    /**
     * 生成元数据
     */
    private DatabaseMetadataDto generateMetadata(MultipartFile file, JacksonDatabase database, Path databasePath) {
        try {
            DatabaseMetadataDto metadata = new DatabaseMetadataDto();

            KdbxStreamFormat streamFormat = (KdbxStreamFormat) database.getStreamFormat();
            KdbxHeader kdbxHeader = streamFormat.getStreamConfiguration();

            metadata.setUploadTime(LocalDateTime.now());
            metadata.setOriginalFileName(file.getOriginalFilename());
            metadata.setFileSize(file.getSize());
            metadata.setEntryCount(keepPassManager.getAllEntries(database).size());
            metadata.setLastModified(LocalDateTime.now());
            metadata.setVersion(String.valueOf(kdbxHeader.getVersion()));
            metadata.setEncryptionAlgorithm(kdbxHeader.getCipherAlgorithm().getName());
            metadata.setKeyDerivation(kdbxHeader.getKeyDerivationFunction().getName());
            kdbxHeader.getKeyDerivationFunction().createKdfParameters();
            metadata.setIterations(2);
            metadata.setMemory(64 * 1024 * 1024);
            metadata.setParallelism(2);
            metadata.setCompression("GZip");
            metadata.setFileHash(calculateFileHash(databasePath));

            return metadata;
        } catch (Exception e) {
            throw new BusinessException("生成元数据失败", e);
        }
    }

    /**
     * 保存元数据
     */
    private void saveMetadata(DatabaseMetadataDto metadata, String userId) {
        try {
            Path metadataPath = getUserMetadataPath(userId);
            objectMapper.writeValue(metadataPath.toFile(), metadata);
            log.info("保存元数据: {}", metadataPath);
        } catch (IOException e) {
            throw new BusinessException("保存元数据失败", e);
        }
    }

    /**
     * 构建上传响应
     */
    private DatabaseUploadResponse buildUploadResponse(DatabaseMetadataDto metadata) {
        DatabaseUploadResponse response = new DatabaseUploadResponse();
        response.setUploadTime(metadata.getUploadTime());
        response.setOriginalFileName(metadata.getOriginalFileName());
        response.setFileSize(metadata.getFileSize());
        response.setEntryCount(metadata.getEntryCount());
        response.setVersion(metadata.getVersion());
        response.setEncryptionAlgorithm(metadata.getEncryptionAlgorithm());
        response.setKeyDerivation(metadata.getKeyDerivation());
        response.setMessage("数据库上传成功");
        return response;
    }

    /**
     * 验证用户主密码
     */
    private void validateMasterPasswordForUser(String masterPassword, String userId) {
        try {
            Path databasePath = getDatabasePath(userId);
            if (!Files.exists(databasePath)) {
                throw new BusinessException("数据库不存在");
            }

            keepPassManager.load(databasePath.toString(), toKdbxCreds(masterPassword));
        } catch (Exception e) {
            throw new BusinessException("主密码错误", e);
        }
    }

    /**
     * 转换为SecurityLite
     */
    private SecurityLite convertToSecurityLite(JacksonEntry entry) {
        SecurityLite lite = new SecurityLite();
        lite.setTitle(entry.getTitle());
        lite.setUsername(entry.getUsername());
        return lite;
    }

    /**
     * 转换为DatabaseMetadata
     */
    private DatabaseMetadata convertToMetadata(DatabaseMetadataDto dto) {
        DatabaseMetadata metadata = new DatabaseMetadata();
        metadata.setUploadTime(dto.getUploadTime());
        metadata.setFileSize(dto.getFileSize());
        metadata.setEntryCount(dto.getEntryCount());
        metadata.setLastModified(dto.getLastModified());
        metadata.setVersion(dto.getVersion());
        metadata.setEncryptionAlgorithm(dto.getEncryptionAlgorithm());
        metadata.setKeyDerivation(dto.getKeyDerivation());
        metadata.setIterations(dto.getIterations());
        metadata.setMemory(dto.getMemory());
        metadata.setParallelism(dto.getParallelism());
        metadata.setCompression(dto.getCompression());
        metadata.setDescription("KeePass数据库");
        metadata.setFileHash(dto.getFileHash());
        return metadata;
    }

    /**
     * 获取用户目录
     */
    private Path getUserDirectory(String userId) {
        return Paths.get(safeboxProperties.getKeepass().getKdbxPath(), userId);
    }

    /**
     * 获取数据库路径
     */
    private Path getDatabasePath(String userId) {
        return getUserDirectory(userId).resolve(DEFAULT_DB_FILE);
    }

    /**
     * 获取元数据文件路径
     */
    private Path getUserMetadataPath(String userId) {
        return getUserDirectory(userId).resolve(METADATA_FILE);
    }

    /**
     * 计算文件哈希值
     */
    private String calculateFileHash(Path filePath) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] fileBytes = Files.readAllBytes(filePath);
            byte[] hashBytes = digest.digest(fileBytes);

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("计算文件哈希值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", path, e);
                        }
                    });
        }
    }

}