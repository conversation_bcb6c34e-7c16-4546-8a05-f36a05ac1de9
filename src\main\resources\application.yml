server:
  port: 8080

spring:
  thymeleaf:
    # 模板文件路径
    prefix: classpath:/templates/
    # 模板文件后缀
    suffix: .html
    # 模板文件编码
    encoding: UTF-8
    # 模板文件类型
    mode: HTML
    # 开发环境下禁用缓存
    cache: false
    # 检查模板是否存在
    check-template: true
    # 检查模板位置是否存在
    check-template-location: true
  # 静态资源配置
  web:
    resources:
      # 静态资源路径
      static-locations: classpath:/static/
      # 缓存时间（开发环境设为0）
      cache:
        period: 0
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

safebox:
  keepass:
    kdbxPath: D:\Workspace\IdeaProjects\safebox\src\main\resources\databases
    kdbxKey: 7FryosZvGTXEt3IEn42FOtMDPif11sMNb8NjrNSxnVM=
    maxFileSize: 3145728  # 3MB in bytes
    allowedExtensions:
      - kdbx
  security:
    max-password-attempts: 3
    lockout-duration: 300  # 5 minutes in seconds
    access-log-enabled: true
    apply-session-timeout: 1800
  lark-bot:
    app-id: cli_a83fdf4b39311013
    app-secret: vXM7DDUZy8P9YCcKwbFlQfYG6LDC5Vug
    security-apply-card-id: AAq9wuGPBGj9w
    process-receive-id: ou_8254cf6ba62c6eb5bbbd1705d5ec0d91
  ec-private-key: egTh+Vmp3IQUbNb7IWm0uJSOhurh98Vx5k6wBV9aCk0=
  ec-public-key: A8nVqa8mxukhtHth3tq8UX+K+KPG2LiK0WVHogiAA64e
  accessPassword: kS2Vmf # 接口访问密码

